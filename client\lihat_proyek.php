<?php
require_once '../includes/session_manager.php';
check_session_auth('client');

$page_title = "Lihat Proyek";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

<?php include 'includes/topbar/topbar.php'; ?>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Daftar Proyek</h1>
                    </div>

                    <!-- DataTales Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Semua Proyek</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Nama Kegiatan</th>
                                            <th>Deskripsi</th>
                                            <th>Tanggal</th>
                                            <th>Status Proyek</th>
                                            <th>Status Verifikasi</th>
                                            <th>Tanggal Submit</th>
                                            <th>Catatan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $no = 1;
                                        $query = mysqli_query($koneksi, "SELECT * FROM tugas_proyek ORDER BY tanggal_submit DESC");
                                        while($row = mysqli_fetch_array($query)) {
                                            // Status proyek badge
                                            $status_proyek_badge = '';
                                            switch($row['status']) {
                                                case 'selesai':
                                                    $status_proyek_badge = '<span class="badge badge-success">Selesai</span>';
                                                    break;
                                                case 'batal':
                                                    $status_proyek_badge = '<span class="badge badge-danger">Batal</span>';
                                                    break;
                                                default:
                                                    $status_proyek_badge = '<span class="badge badge-warning">Proses</span>';
                                            }
                                            
                                            // Status verifikasi badge
                                            $status_verifikasi_badge = '';
                                            switch($row['status_verifikasi']) {
                                                case 'approved':
                                                    $status_verifikasi_badge = '<span class="badge badge-success">Disetujui</span>';
                                                    break;
                                                case 'rejected':
                                                    $status_verifikasi_badge = '<span class="badge badge-danger">Ditolak</span>';
                                                    break;
                                                default:
                                                    $status_verifikasi_badge = '<span class="badge badge-warning">Pending</span>';
                                            }
                                            
                                            echo "<tr>";
                                            echo "<td>" . $no++ . "</td>";
                                            echo "<td>" . htmlspecialchars($row['nama_kegiatan']) . "</td>";
                                            echo "<td>" . htmlspecialchars($row['deskripsi']) . "</td>";
                                            echo "<td>" . date('d/m/Y', strtotime($row['tgl'])) . "</td>";
                                            echo "<td>" . $status_proyek_badge . "</td>";
                                            echo "<td>" . $status_verifikasi_badge . "</td>";
                                            echo "<td>" . date('d/m/Y H:i', strtotime($row['tanggal_submit'])) . "</td>";
                                            echo "<td>" . ($row['catatan_verifikasi'] ? htmlspecialchars($row['catatan_verifikasi']) : '-') . "</td>";
                                            echo "</tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

<?php include 'includes/footer/footer.php'; ?>
