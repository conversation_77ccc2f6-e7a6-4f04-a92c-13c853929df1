# Sistem Login Multi-Role Antosa Arsitek

Sistem login dengan 3 role berbeda: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan <PERSON>.

## Struktur Database

### Tabel `petugas`
- Untuk login Admin dan Proyek
- Fields: id_petugas, nama_petugas, username, password, level

### Tabel `users` 
- Untuk login Client
- Fields: id, first_name, last_name, email, password (hashed), role

### Tabel `tugas_proyek`
- Menyimpan data proyek
- Fields: id, nama_kegiatan, deskripsi, tgl, status, status_verifikasi, dll

### Tabel `file_gambar`
- Menyimpan data file gambar
- Fields: id, deskripsi, gambar, status_verifikasi, dll

## Akun Login Default

### Admin
- Username: `ian`
- Password: `11111`

### Proyek
- Username: `lingga`
- Password: `11111`

### Client (setelah menjalankan create_client_user.php)
- Email: `<EMAIL>`
- Password: `client123`

## Setup

1. Import database `arsitek.sql`
2. <PERSON><PERSON><PERSON> `create_client_user.php` untuk membuat user client
3. J<PERSON><PERSON> `insert_sample_data.php` untuk menambah data sample
4. Akses `index.php` untuk login

## Fitur

### Admin
- Dashboard admin
- Kelola user proyek
- Verifikasi tugas dan file

### Proyek
- Dashboard proyek
- Input tugas harian
- Upload file
- Lihat verifikasi

### Client
- Dashboard client (view-only)
- Lihat semua proyek
- Lihat semua file
- Profile management

## Struktur Folder

```
/
├── admin/              # Halaman admin
├── client/             # Halaman client (baru)
├── proyek/             # Halaman proyek
├── login/              # Assets login page
├── includes/           # Shared components
├── tmp/                # SB Admin 2 template assets
├── index.php           # Login page
├── cek_login.php       # Login processor
├── logout.php          # Logout handler
└── koneksi.php         # Database connection
```

## Teknologi

- PHP 7.4+
- MySQL/MariaDB
- Bootstrap 4 (SB Admin 2 Template)
- jQuery
- Font Awesome

## Keamanan

- Password hashing untuk tabel users
- Session management
- SQL injection protection dengan prepared statements
- Access control berdasarkan role
