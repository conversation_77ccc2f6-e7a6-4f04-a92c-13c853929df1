<?php
require_once '../includes/session_manager.php';
check_session_auth('client');

$page_title = "Profile";
include 'includes/header/header.php';

// Get user data
$user_id = $_SESSION['id_user'];
$query_user = mysqli_query($koneksi, "SELECT * FROM users WHERE id = '$user_id'");
$user_data = mysqli_fetch_array($query_user);
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

<?php include 'includes/topbar/topbar.php'; ?>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Profile Saya</h1>
                    </div>

                    <div class="row">
                        <!-- Profile Card -->
                        <div class="col-lg-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Informasi Profile</h6>
                                </div>
                                <div class="card-body">
                                    <div class="text-center mb-4">
                                        <img class="img-profile rounded-circle" src="../tmp/img/undraw_profile.svg" style="width: 120px; height: 120px;">
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <strong>Nama Depan:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <?php echo htmlspecialchars($user_data['first_name']); ?>
                                        </div>
                                    </div>
                                    <hr>
                                    
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <strong>Nama Belakang:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <?php echo htmlspecialchars($user_data['last_name']); ?>
                                        </div>
                                    </div>
                                    <hr>
                                    
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <strong>Email:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <?php echo htmlspecialchars($user_data['email']); ?>
                                        </div>
                                    </div>
                                    <hr>
                                    
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <strong>Role:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="badge badge-info"><?php echo ucfirst($user_data['role']); ?></span>
                                        </div>
                                    </div>
                                    <hr>
                                    
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <strong>Bergabung:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <?php echo date('d F Y', strtotime($user_data['created_at'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Card -->
                        <div class="col-lg-6">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Statistik Aktivitas</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6 text-center">
                                            <div class="card border-left-primary">
                                                <div class="card-body py-3">
                                                    <div class="h4 mb-0 font-weight-bold text-primary">
                                                        <?php
                                                        $query_total = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek");
                                                        echo mysqli_fetch_array($query_total)['total'];
                                                        ?>
                                                    </div>
                                                    <div class="text-xs font-weight-bold text-uppercase">Total Proyek</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6 text-center">
                                            <div class="card border-left-success">
                                                <div class="card-body py-3">
                                                    <div class="h4 mb-0 font-weight-bold text-success">
                                                        <?php
                                                        $query_approved = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek WHERE status_verifikasi='approved'");
                                                        echo mysqli_fetch_array($query_approved)['total'];
                                                        ?>
                                                    </div>
                                                    <div class="text-xs font-weight-bold text-uppercase">Disetujui</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-6 text-center">
                                            <div class="card border-left-warning">
                                                <div class="card-body py-3">
                                                    <div class="h4 mb-0 font-weight-bold text-warning">
                                                        <?php
                                                        $query_pending = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek WHERE status_verifikasi='pending'");
                                                        echo mysqli_fetch_array($query_pending)['total'];
                                                        ?>
                                                    </div>
                                                    <div class="text-xs font-weight-bold text-uppercase">Pending</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6 text-center">
                                            <div class="card border-left-info">
                                                <div class="card-body py-3">
                                                    <div class="h4 mb-0 font-weight-bold text-info">
                                                        <?php
                                                        $query_files = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM file_gambar");
                                                        echo mysqli_fetch_array($query_files)['total'];
                                                        ?>
                                                    </div>
                                                    <div class="text-xs font-weight-bold text-uppercase">Total File</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

<?php include 'includes/footer/footer.php'; ?>
