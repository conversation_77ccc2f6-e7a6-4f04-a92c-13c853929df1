<?php
/**
 * Session Manager - Prevents duplicate session_start() calls
 * Use this instead of calling session_start() directly
 */

function safe_session_start() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

function check_session_auth($required_level = null) {
    safe_session_start();

    if (!isset($_SESSION['nama'])) {
        // Determine correct path to index.php based on current location
        $redirect_path = (strpos($_SERVER['REQUEST_URI'], '/client/') !== false ||
                         strpos($_SERVER['REQUEST_URI'], '/admin/') !== false ||
                         strpos($_SERVER['REQUEST_URI'], '/proyek/') !== false)
                         ? "../index.php" : "index.php";
        header("Location: " . $redirect_path);
        exit;
    }

    // Check for specific level access
    if ($required_level) {
        // Determine correct path to index.php based on current location
        $redirect_path = (strpos($_SERVER['REQUEST_URI'], '/client/') !== false ||
                         strpos($_SERVER['REQUEST_URI'], '/admin/') !== false ||
                         strpos($_SERVER['REQUEST_URI'], '/proyek/') !== false)
                         ? "../index.php" : "index.php";

        // For client access, check both level and user_type
        if ($required_level == 'client') {
            if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] != 'client') {
                echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='" . $redirect_path . "';</script>";
                exit;
            }
        } else {
            // For admin/proyek access, check level from petugas table
            if ($_SESSION['level'] != $required_level) {
                echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='" . $redirect_path . "';</script>";
                exit;
            }
        }
    }
}

function destroy_session() {
    safe_session_start();
    unset($_SESSION['nama']);
    session_destroy();
}
?>
