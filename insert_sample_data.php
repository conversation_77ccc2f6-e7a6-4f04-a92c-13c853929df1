<?php
// Script untuk menambahkan data sample untuk testing
require 'koneksi.php';

// Insert sample tugas_proyek
$sample_projects = [
    [
        'nama_kegiatan' => 'Desain <PERSON>umah <PERSON>',
        'deskripsi' => 'Membuat desain rumah minimalis 2 lantai dengan 3 kamar tidur',
        'tgl' => '2025-06-20',
        'status' => 'proses',
        'status_verifikasi' => 'approved'
    ],
    [
        'nama_kegiatan' => 'Renovasi Kantor',
        'deskripsi' => 'Renovasi interior kantor dengan konsep modern',
        'tgl' => '2025-06-22',
        'status' => 'selesai',
        'status_verifikasi' => 'approved'
    ],
    [
        'nama_kegiatan' => 'Desain Taman',
        'deskripsi' => 'Membuat desain taman dengan konsep natural',
        'tgl' => '2025-06-25',
        'status' => 'proses',
        'status_verifikasi' => 'pending'
    ]
];

foreach ($sample_projects as $project) {
    $sql = "INSERT INTO tugas_proyek (nama_kegiatan, deskripsi, tgl, status, status_verifikasi) VALUES (?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($koneksi, $sql);
    mysqli_stmt_bind_param($stmt, "sssss", 
        $project['nama_kegiatan'], 
        $project['deskripsi'], 
        $project['tgl'], 
        $project['status'], 
        $project['status_verifikasi']
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "Project '" . $project['nama_kegiatan'] . "' berhasil ditambahkan<br>";
    } else {
        echo "Error adding project: " . mysqli_error($koneksi) . "<br>";
    }
    mysqli_stmt_close($stmt);
}

// Insert sample file_gambar
$sample_files = [
    [
        'deskripsi' => 'Denah Lantai 1 Rumah Minimalis',
        'gambar' => 'denah_lantai1.jpg',
        'status_verifikasi' => 'approved'
    ],
    [
        'deskripsi' => 'Tampak Depan Rumah',
        'gambar' => 'tampak_depan.jpg',
        'status_verifikasi' => 'approved'
    ],
    [
        'deskripsi' => 'Interior Ruang Tamu',
        'gambar' => 'interior_ruangtamu.jpg',
        'status_verifikasi' => 'pending'
    ]
];

foreach ($sample_files as $file) {
    $sql = "INSERT INTO file_gambar (deskripsi, gambar, status_verifikasi) VALUES (?, ?, ?)";
    $stmt = mysqli_prepare($koneksi, $sql);
    mysqli_stmt_bind_param($stmt, "sss", 
        $file['deskripsi'], 
        $file['gambar'], 
        $file['status_verifikasi']
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "File '" . $file['deskripsi'] . "' berhasil ditambahkan<br>";
    } else {
        echo "Error adding file: " . mysqli_error($koneksi) . "<br>";
    }
    mysqli_stmt_close($stmt);
}

echo "<br><strong>Sample data berhasil ditambahkan!</strong><br>";
echo "<a href='index.php'>Kembali ke Login</a>";

mysqli_close($koneksi);
?>
