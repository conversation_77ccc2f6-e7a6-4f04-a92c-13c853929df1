<?php
require 'koneksi.php';
require_once 'includes/session_manager.php';
safe_session_start();

// Ambil input dari form
$user = trim($_POST['username']);
$pass = trim($_POST['password']);

// Cek login di tabel petugas terlebih dahulu
$sql_petugas = mysqli_query($koneksi, "SELECT * FROM petugas WHERE username='$user' AND password='$pass'");

if (!$sql_petugas) {
    die("Query Error: " . mysqli_error($koneksi));
}

$cek_petugas = mysqli_num_rows($sql_petugas);

if ($cek_petugas > 0) {
    // Login sebagai petugas (admin/proyek)
    $data = mysqli_fetch_array($sql_petugas);

    $_SESSION['id_petugas'] = $data['id_petugas'];
    $_SESSION['user'] = $user;
    $_SESSION['nama'] = $data['nama_petugas'];
    $_SESSION['level'] = $data['level'];
    $_SESSION['user_type'] = 'petugas';

    // Arahkan sesuai level user
    if ($data['level'] == "proyek") {
        header('Location:proyek/proyek.php');
    } elseif ($data['level'] == "admin") {
        header('Location:admin/admin.php');
    } else {
        echo "<script>alert('Level tidak dikenali!'); window.location='index.php';</script>";
    }
} else {
    // Cek login di tabel users (client)
    $sql_users = mysqli_query($koneksi, "SELECT * FROM users WHERE email='$user'");

    if (!$sql_users) {
        die("Query Error: " . mysqli_error($koneksi));
    }

    $cek_users = mysqli_num_rows($sql_users);

    if ($cek_users > 0) {
        $data = mysqli_fetch_array($sql_users);

        // Verify password using password_verify for hashed passwords
        if (password_verify($pass, $data['password'])) {
            // Login sebagai client
            $_SESSION['id_user'] = $data['id'];
            $_SESSION['user'] = $data['email'];
            $_SESSION['nama'] = $data['first_name'] . ' ' . $data['last_name'];
            $_SESSION['level'] = $data['role'];
            $_SESSION['user_type'] = 'client';

            // Arahkan ke halaman client
            header('Location:client/client.php');
        } else {
            echo "<script>alert('Username/Email atau Password tidak ditemukan'); window.location='index.php';</script>";
        }
    } else {
        echo "<script>alert('Username/Email atau Password tidak ditemukan'); window.location='index.php';</script>";
    }
}
?>
