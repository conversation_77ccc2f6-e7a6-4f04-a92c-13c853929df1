                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Dashboard Client</h1>
                    </div>

                    <!-- Content Row -->
                    <div class="row">

                        <!-- Project Status Card -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total Proyek</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php
                                                $query_total = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek");
                                                $total_proyek = mysqli_fetch_array($query_total)['total'];
                                                echo $total_proyek;
                                                ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Approved Projects Card -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Proyek Disetujui</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php
                                                $query_approved = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek WHERE status_verifikasi='approved'");
                                                $approved_proyek = mysqli_fetch_array($query_approved)['total'];
                                                echo $approved_proyek;
                                                ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pending Projects Card -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Menunggu Verifikasi</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php
                                                $query_pending = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM tugas_proyek WHERE status_verifikasi='pending'");
                                                $pending_proyek = mysqli_fetch_array($query_pending)['total'];
                                                echo $pending_proyek;
                                                ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Files Card -->
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Total File</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?php
                                                $query_files = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM file_gambar");
                                                $total_files = mysqli_fetch_array($query_files)['total'];
                                                echo $total_files;
                                                ?>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-file-image fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Content Row -->
                    <div class="row">

                        <!-- Recent Projects -->
                        <div class="col-lg-6 mb-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Proyek Terbaru</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th>Nama Kegiatan</th>
                                                    <th>Tanggal</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $query_recent = mysqli_query($koneksi, "SELECT * FROM tugas_proyek ORDER BY tanggal_submit DESC LIMIT 5");
                                                while($row = mysqli_fetch_array($query_recent)) {
                                                    $status_badge = '';
                                                    switch($row['status_verifikasi']) {
                                                        case 'approved':
                                                            $status_badge = '<span class="badge badge-success">Disetujui</span>';
                                                            break;
                                                        case 'rejected':
                                                            $status_badge = '<span class="badge badge-danger">Ditolak</span>';
                                                            break;
                                                        default:
                                                            $status_badge = '<span class="badge badge-warning">Pending</span>';
                                                    }
                                                    echo "<tr>";
                                                    echo "<td>" . htmlspecialchars($row['nama_kegiatan']) . "</td>";
                                                    echo "<td>" . date('d/m/Y', strtotime($row['tgl'])) . "</td>";
                                                    echo "<td>" . $status_badge . "</td>";
                                                    echo "</tr>";
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Files -->
                        <div class="col-lg-6 mb-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">File Terbaru</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" width="100%" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th>Deskripsi</th>
                                                    <th>Tanggal</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $query_files_recent = mysqli_query($koneksi, "SELECT * FROM file_gambar ORDER BY tanggal_submit DESC LIMIT 5");
                                                while($row = mysqli_fetch_array($query_files_recent)) {
                                                    $status_badge = '';
                                                    switch($row['status_verifikasi']) {
                                                        case 'approved':
                                                            $status_badge = '<span class="badge badge-success">Disetujui</span>';
                                                            break;
                                                        case 'rejected':
                                                            $status_badge = '<span class="badge badge-danger">Ditolak</span>';
                                                            break;
                                                        default:
                                                            $status_badge = '<span class="badge badge-warning">Pending</span>';
                                                    }
                                                    echo "<tr>";
                                                    echo "<td>" . htmlspecialchars($row['deskripsi']) . "</td>";
                                                    echo "<td>" . date('d/m/Y', strtotime($row['tanggal_submit'])) . "</td>";
                                                    echo "<td>" . $status_badge . "</td>";
                                                    echo "</tr>";
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
