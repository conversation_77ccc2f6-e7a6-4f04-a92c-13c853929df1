<?php
// Script untuk membuat user client untuk testing
require 'koneksi.php';

// Data client untuk testing
$first_name = 'Client';
$last_name = 'Test';
$email = '<EMAIL>';
$password = 'client123'; // Password akan di-hash
$role = 'client';

// Hash password
$hashed_password = password_hash($password, PASSWORD_DEFAULT);

// Insert ke database
$sql = "INSERT INTO users (first_name, last_name, email, password, role) VALUES (?, ?, ?, ?, ?)";
$stmt = mysqli_prepare($koneksi, $sql);
mysqli_stmt_bind_param($stmt, "sssss", $first_name, $last_name, $email, $hashed_password, $role);

if (mysqli_stmt_execute($stmt)) {
    echo "User client berhasil dibuat!<br>";
    echo "Email: " . $email . "<br>";
    echo "Password: " . $password . "<br>";
    echo "<br><a href='index.php'><PERSON>gin <PERSON></a>";
} else {
    echo "Error: " . mysqli_error($koneksi);
}

mysqli_stmt_close($stmt);
mysqli_close($koneksi);
?>
