<?php
require_once '../includes/session_manager.php';
check_session_auth('client');

$page_title = "Lihat File";
include 'includes/header/header.php';
?>

<?php include 'includes/sidebar/sidebar.php'; ?>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

<?php include 'includes/topbar/topbar.php'; ?>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Daftar File Gambar</h1>
                    </div>

                    <!-- DataTales Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Semua File Gambar</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Deskripsi</th>
                                            <th>File Gambar</th>
                                            <th>Status Verifikasi</th>
                                            <th>Tanggal Submit</th>
                                            <th>Tanggal Verifikasi</th>
                                            <th>Catatan</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $no = 1;
                                        $query = mysqli_query($koneksi, "SELECT * FROM file_gambar ORDER BY tanggal_submit DESC");
                                        while($row = mysqli_fetch_array($query)) {
                                            // Status verifikasi badge
                                            $status_badge = '';
                                            switch($row['status_verifikasi']) {
                                                case 'approved':
                                                    $status_badge = '<span class="badge badge-success">Disetujui</span>';
                                                    break;
                                                case 'rejected':
                                                    $status_badge = '<span class="badge badge-danger">Ditolak</span>';
                                                    break;
                                                default:
                                                    $status_badge = '<span class="badge badge-warning">Pending</span>';
                                            }
                                            
                                            echo "<tr>";
                                            echo "<td>" . $no++ . "</td>";
                                            echo "<td>" . htmlspecialchars($row['deskripsi']) . "</td>";
                                            echo "<td>" . htmlspecialchars($row['gambar']) . "</td>";
                                            echo "<td>" . $status_badge . "</td>";
                                            echo "<td>" . date('d/m/Y H:i', strtotime($row['tanggal_submit'])) . "</td>";
                                            echo "<td>" . ($row['tanggal_verifikasi'] ? date('d/m/Y H:i', strtotime($row['tanggal_verifikasi'])) : '-') . "</td>";
                                            echo "<td>" . ($row['catatan_verifikasi'] ? htmlspecialchars($row['catatan_verifikasi']) : '-') . "</td>";
                                            echo "<td>";
                                            if(file_exists("../proyek/uploads/" . $row['gambar'])) {
                                                echo '<a href="../proyek/uploads/' . $row['gambar'] . '" target="_blank" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i> Lihat
                                                      </a>';
                                            } else {
                                                echo '<span class="text-muted">File tidak ditemukan</span>';
                                            }
                                            echo "</td>";
                                            echo "</tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

<?php include 'includes/footer/footer.php'; ?>
